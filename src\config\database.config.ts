import mysql from 'mysql2/promise';
import { MongoClient, Db } from 'mongodb';

export interface DatabaseConfig {
  mysql: {
    host: string;
    user: string;
    password: string;
    database: string;
    port: number;
    connectionLimit: number;
    acquireTimeout: number;
    timeout: number;
  };
  mongodb: {
    uri: string;
    database: string;
    options: {
      maxPoolSize: number;
      serverSelectionTimeoutMS: number;
      socketTimeoutMS: number;
    };
  };
}

export const dbConfig: DatabaseConfig = {
  mysql: {
    host: 'localhost',
    user: 'root',
    password: '', 
    database: 'lms',
    port: 3306,
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
    database: 'lms_cloud',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
  },
};

let mysqlPool: mysql.Pool | null = null;

let mongoClient: MongoClient | null = null;
let mongoDb: Db | null = null;

export const initMySQLConnection = async (): Promise<mysql.Pool> => {
  try {
    if (!mysqlPool) {
      mysqlPool = mysql.createPool(dbConfig.mysql);
      
      const connection = await mysqlPool.getConnection();
      console.log('✅ MySQL connected successfully');
      connection.release();
      
      await createDatabase();
      await createTables();
    }
    return mysqlPool;
  } catch (error) {
    console.error('❌ MySQL connection failed:', error);
    throw new Error('Failed to connect to MySQL database');
  }
};

export const initMongoConnection = async (): Promise<Db> => {
  try {
    if (!mongoClient) {
      mongoClient = new MongoClient(dbConfig.mongodb.uri, dbConfig.mongodb.options);
      await mongoClient.connect();
      mongoDb = mongoClient.db(dbConfig.mongodb.database);
      console.log('✅ MongoDB connected successfully');
    }
    return mongoDb!;
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    throw new Error('Failed to connect to MongoDB database');
  }
};

export const getMySQLConnection = async (): Promise<mysql.Pool> => {
  if (!mysqlPool) {
    return await initMySQLConnection();
  }
  return mysqlPool;
};

export const getMongoConnection = async (): Promise<Db> => {
  if (!mongoDb) {
    return await initMongoConnection();
  }
  return mongoDb;
};

const createDatabase = async (): Promise<void> => {
  try {
    const tempConfig = { ...dbConfig.mysql };
    delete (tempConfig as any).database;
    
    const tempPool = mysql.createPool(tempConfig);
    const connection = await tempPool.getConnection();
    
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.mysql.database}\``);
    console.log(`✅ Database '${dbConfig.mysql.database}' created/verified`);
    
    connection.release();
    await tempPool.end();
  } catch (error) {
    console.error('❌ Error creating database:', error);
    throw error;
  }
};


const createTables = async (): Promise<void> => {
  try {
    const pool = await getMySQLConnection();
    
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        role ENUM('admin', 'instructor', 'student') NOT NULL,
        avatar VARCHAR(500),
        institution VARCHAR(255),
        department VARCHAR(255),
        student_id VARCHAR(50),
        employee_id VARCHAR(50),
        permissions JSON,
        is_active BOOLEAN DEFAULT TRUE,
        is_verified BOOLEAN DEFAULT FALSE,
        kyc_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
        wallet_balance DECIMAL(10,2) DEFAULT 0.00,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_student_id (student_id),
        INDEX idx_employee_id (employee_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    await pool.execute(`
      CREATE TABLE IF NOT EXISTS modules (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        type ENUM('cbt', 'lms', 'attendance', 'gradebook', 'library', 'fee_management', 'timetable') NOT NULL,
        icon VARCHAR(100),
        is_active BOOLEAN DEFAULT TRUE,
        settings JSON,
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_type (type),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('✅ Database tables created/verified successfully');
  } catch (error) {
    console.error('❌ Error creating tables:', error);
    throw error;
  }
};

// Close connections
export const closeDatabaseConnections = async (): Promise<void> => {
  try {
    if (mysqlPool) {
      await mysqlPool.end();
      mysqlPool = null;
      console.log('✅ MySQL connection closed');
    }
    
    if (mongoClient) {
      await mongoClient.close();
      mongoClient = null;
      mongoDb = null;
      console.log('✅ MongoDB connection closed');
    }
  } catch (error) {
    console.error('❌ Error closing database connections:', error);
  }
};

// Health check
export const checkDatabaseHealth = async (): Promise<{ mysql: boolean; mongodb: boolean }> => {
  const health = { mysql: false, mongodb: false };
  
  try {
    const pool = await getMySQLConnection();
    const [rows] = await pool.execute('SELECT 1');
    health.mysql = true;
  } catch (error) {
    console.error('MySQL health check failed:', error);
  }
  
  try {
    const db = await getMongoConnection();
    await db.admin().ping();
    health.mongodb = true;
  } catch (error) {
    console.error('MongoDB health check failed:', error);
  }
  
  return health;
};
