{"name": "cbt-system-backend", "version": "1.0.0", "description": "CBT System Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "keywords": ["cbt", "education", "testing", "api"], "author": "CBT System Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "mysql2": "^3.6.5", "mongodb": "^6.3.0", "exceljs": "^4.4.0", "xml2js": "^0.6.2", "sharp": "^0.33.1", "node-cron": "^3.0.3", "ws": "^8.14.2", "zod": "^3.22.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/ws": "^8.5.10", "@types/xml2js": "^0.4.14", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.2", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}