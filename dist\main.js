"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const isDev = __importStar(require("electron-is-dev"));
class ElectronApp {
    constructor() {
        this.mainWindow = null;
        this.initializeApp();
    }
    initializeApp() {
        // Handle app ready
        electron_1.app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();
            this.setupIPC();
            electron_1.app.on('activate', () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });
        // Handle window closed
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        // Security: Prevent new window creation
        electron_1.app.on('web-contents-created', (_, contents) => {
            contents.setWindowOpenHandler(({ url }) => {
                electron_1.shell.openExternal(url);
                return { action: 'deny' };
            });
        });
    }
    createMainWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js'),
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false
            },
            icon: path.join(__dirname, '../assets/icon.png'),
            show: false,
            titleBarStyle: 'default',
            frame: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true
        });
        // Load the app
        const startUrl = isDev
            ? 'http://localhost:3000'
            : `file://${path.join(__dirname, '../out/index.html')}`;
        this.mainWindow.loadURL(startUrl);
        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow?.show();
            if (isDev) {
                this.mainWindow?.webContents.openDevTools();
            }
        });
        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        // Prevent navigation to external URLs
        this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
            const parsedUrl = new URL(navigationUrl);
            if (parsedUrl.origin !== 'http://localhost:3000' && !navigationUrl.startsWith('file://')) {
                event.preventDefault();
            }
        });
    }
    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'Quit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        }
                    }
                ]
            },
            {
                label: 'Edit',
                submenu: [
                    { role: 'undo' },
                    { role: 'redo' },
                    { type: 'separator' },
                    { role: 'cut' },
                    { role: 'copy' },
                    { role: 'paste' }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' }
                ]
            }
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    setupIPC() {
        // Handle authentication
        electron_1.ipcMain.handle('auth:login', async (_, credentials) => {
            // Dummy authentication logic
            const { email, password } = credentials;
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            // Dummy validation
            if (email && password) {
                return {
                    success: true,
                    user: {
                        id: '1',
                        email: email,
                        name: 'John Doe',
                        role: 'user'
                    },
                    token: 'dummy-jwt-token'
                };
            }
            else {
                return {
                    success: false,
                    message: 'Invalid credentials'
                };
            }
        });
        // Handle logout
        electron_1.ipcMain.handle('auth:logout', async () => {
            return { success: true };
        });
        // Handle app info
        electron_1.ipcMain.handle('app:getInfo', () => {
            return {
                name: electron_1.app.getName(),
                version: electron_1.app.getVersion(),
                platform: process.platform
            };
        });
        // Handle window controls
        electron_1.ipcMain.handle('window:minimize', () => {
            this.mainWindow?.minimize();
        });
        electron_1.ipcMain.handle('window:maximize', () => {
            if (this.mainWindow?.isMaximized()) {
                this.mainWindow.unmaximize();
            }
            else {
                this.mainWindow?.maximize();
            }
        });
        electron_1.ipcMain.handle('window:close', () => {
            this.mainWindow?.close();
        });
    }
}
// Initialize the app
new ElectronApp();
