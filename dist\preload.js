"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI = {
    auth: {
        login: (credentials) => electron_1.ipcRenderer.invoke('auth:login', credentials),
        logout: () => electron_1.ipcRenderer.invoke('auth:logout')
    },
    app: {
        getInfo: () => electron_1.ipcRenderer.invoke('app:getInfo')
    },
    window: {
        minimize: () => electron_1.ipcRenderer.invoke('window:minimize'),
        maximize: () => electron_1.ipcRenderer.invoke('window:maximize'),
        close: () => electron_1.ipcRenderer.invoke('window:close')
    }
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
