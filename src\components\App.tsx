import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import { ThemeProvider } from '../contexts/ThemeContext';
import { AnimatedWelcomeScreen } from './welcome/AnimatedWelcomeScreen';
import { LoginScreen } from './auth/LoginScreen';
import { StudentDashboard } from './dashboards/StudentDashboard';
import { InstructorDashboard } from './dashboards/InstructorDashboard';
import { AdminDashboard } from './dashboards/AdminDashboard';
import { LoadingScreen } from './shared/LoadingScreen';
import { clientDatabaseService } from '../services/client-database.service';
import '../styles/theme.css';

type AppState = 'welcome' | 'login' | 'dashboard';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'student' | 'instructor' | 'admin';
  avatar?: string;
}

interface LoginCredentials {
  identifier: string;
  password: string;
  rememberMe: boolean;
}

interface LoginResult {
  success: boolean;
  user?: User;
  message?: string;
}

// Main App Content Component
const AppContent: React.FC = () => {
  const [appState, setAppState] = useState<AppState>('welcome');
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize database on app start
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 Starting CBT System Application...');
        await clientDatabaseService.initialize();
        console.log('✅ CBT System Application initialized successfully');
        console.log('🎯 Ready for user authentication and testing');
      } catch (error) {
        console.error('❌ Failed to initialize CBT application:', error);
      }
    };

    initializeApp();
  }, []);

  const handleWelcomeComplete = () => {
    setAppState('login');
  };

  const handleWelcomeSkip = () => {
    setAppState('login');
  };

  const handleLogin = async (credentials: LoginCredentials): Promise<LoginResult> => {
    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Check credentials against demo users
      const demoUsers = [
        { id: '1', name: 'John Student', email: '<EMAIL>', password: 'demo123', role: 'student' as const },
        { id: '2', name: 'Jane Instructor', email: '<EMAIL>', password: 'demo123', role: 'instructor' as const },
        { id: '3', name: 'Admin User', email: '<EMAIL>', password: 'demo123', role: 'admin' as const }
      ];

      const foundUser = demoUsers.find(u =>
        (u.email === credentials.identifier || u.id === credentials.identifier) &&
        u.password === credentials.password
      );

      if (foundUser) {
        const userData: User = {
          id: foundUser.id,
          name: foundUser.name,
          email: foundUser.email,
          role: foundUser.role
        };

        setUser(userData);
        setAppState('dashboard');

        return { success: true, user: userData };
      } else {
        return { success: false, message: 'Invalid credentials. Please check your email/ID and password.' };
      }
    } catch (error) {
      return { success: false, message: 'Login failed. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    setUser(null);
    setAppState('welcome');
  };

  const handleBackToWelcome = () => {
    // If user is logged in, don't go back to welcome, go to login instead
    if (user) {
      handleLogout();
    } else {
      setAppState('welcome');
    }
  };

  const renderDashboard = () => {
    if (!user) return null;

    switch (user.role) {
      case 'student':
        return (
          <StudentDashboard
            user={user}
            onBack={handleBackToWelcome}
            onLogout={handleLogout}
          />
        );
      case 'instructor':
        return (
          <InstructorDashboard
            user={user}
            onBack={handleBackToWelcome}
            onLogout={handleLogout}
          />
        );
      case 'admin':
        return (
          <AdminDashboard
            user={user}
            onBack={handleBackToWelcome}
            onLogout={handleLogout}
          />
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="app">
      <AnimatePresence mode="wait">
        {appState === 'welcome' && (
          <AnimatedWelcomeScreen
            key="welcome"
            onComplete={handleWelcomeComplete}
            onSkip={handleWelcomeSkip}
          />
        )}

        {appState === 'login' && (
          <LoginScreen
            key="login"
            onLogin={handleLogin}
            onBack={handleBackToWelcome}
          />
        )}

        {appState === 'dashboard' && user && (
          <div key={`dashboard-${user.role}`}>
            {renderDashboard()}
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Main App Component with Providers
export const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
};
