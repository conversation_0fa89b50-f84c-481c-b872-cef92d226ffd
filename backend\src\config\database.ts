import { PrismaClient } from '@prisma/client';
import { MongoClient } from 'mongodb';
import mysql from 'mysql2/promise';
import { logger } from '../utils/logger';

// Prisma client for main database operations
export const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

// MongoDB client for cloud sync
let mongoClient: MongoClient | null = null;

// MySQL connection pool for direct queries
let mysqlPool: mysql.Pool | null = null;

export async function connectDatabase() {
  try {
    // Connect to main database via Prisma
    await prisma.$connect();
    logger.info('✅ Connected to main database via Prisma');

    // Setup MySQL connection pool if needed
    if (process.env.MYSQL_URL) {
      mysqlPool = mysql.createPool({
        uri: process.env.MYSQL_URL,
        connectionLimit: 10,
        queueLimit: 0,
        acquireTimeout: 60000,
        timeout: 60000,
      });
      logger.info('✅ MySQL connection pool created');
    }

    // Connect to MongoDB for cloud sync if configured
    if (process.env.MONGODB_URL) {
      mongoClient = new MongoClient(process.env.MONGODB_URL);
      await mongoClient.connect();
      logger.info('✅ Connected to MongoDB for cloud sync');
    }

    // Test connections
    await testConnections();
    
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    throw error;
  }
}

export async function disconnectDatabase() {
  try {
    await prisma.$disconnect();
    logger.info('✅ Disconnected from main database');

    if (mongoClient) {
      await mongoClient.close();
      logger.info('✅ Disconnected from MongoDB');
    }

    if (mysqlPool) {
      await mysqlPool.end();
      logger.info('✅ MySQL connection pool closed');
    }
  } catch (error) {
    logger.error('❌ Error disconnecting from databases:', error);
  }
}

async function testConnections() {
  try {
    // Test Prisma connection
    await prisma.$queryRaw`SELECT 1`;
    logger.info('✅ Prisma connection test passed');

    // Test MongoDB connection
    if (mongoClient) {
      await mongoClient.db('cbt_sync').command({ ping: 1 });
      logger.info('✅ MongoDB connection test passed');
    }

    // Test MySQL pool connection
    if (mysqlPool) {
      const connection = await mysqlPool.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
      logger.info('✅ MySQL pool connection test passed');
    }
  } catch (error) {
    logger.error('❌ Database connection test failed:', error);
    throw error;
  }
}

// Database health check
export async function checkDatabaseHealth() {
  const health = {
    prisma: false,
    mongodb: false,
    mysql: false,
    timestamp: new Date().toISOString()
  };

  try {
    // Check Prisma
    await prisma.$queryRaw`SELECT 1`;
    health.prisma = true;
  } catch (error) {
    logger.error('Prisma health check failed:', error);
  }

  try {
    // Check MongoDB
    if (mongoClient) {
      await mongoClient.db('cbt_sync').command({ ping: 1 });
      health.mongodb = true;
    }
  } catch (error) {
    logger.error('MongoDB health check failed:', error);
  }

  try {
    // Check MySQL
    if (mysqlPool) {
      const connection = await mysqlPool.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
      health.mysql = true;
    }
  } catch (error) {
    logger.error('MySQL health check failed:', error);
  }

  return health;
}

// Export clients for use in services
export { mongoClient, mysqlPool };

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectDatabase();
});
